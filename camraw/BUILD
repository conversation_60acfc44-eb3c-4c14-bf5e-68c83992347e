cc_library(
   name = "camraw",
   srcs = [
           "bilinear_demosaic_processor.cc", "bit_pump.cc",
           "color_space_processor.cc", "dng_file_parser.cc",
           "dng_pipeline_source.cc", "file_data_accessor.cc",
           "file_type_classifier.cc", "file_type_registry.cc",
           "huffman_table.cc", "imagemeta.cc", "jpeg_bit_pump.cc",
           "lossless_jpeg_decoder.cc", "memory_data_accessor.cc",
           "pipeline.cc", "pipeline_accumulator.cc",
           "pipeline_task_runner.cc", "scale_data_processor.cc",
           "sony_raw_file_parser.cc", "sony_raw_pipeline_source.cc",
           "tiff_export_sink.cc", "tiff_file_parser.cc", "tiff_ifd.cc",
           "tiff_tag.cc", "tiff_pipeline_source.cc", "tone_curve_processor.cc",
           "white_balance_processor.cc"],
   hdrs = [
           "bilinear_demosaic_processor.h", "bit_pump.h", "colorspace.h",
           "color_space_processor.h", "data_accessor.h", "dng_file_parser.h",
           "dng_pipeline_source.h", "endian.h", "file_data_accessor.h",
           "file_parser.h", "file_type_classifier.h", "file_type_registry.h",
           "jpeg.h", "jpeg_bit_pump.h", "huffman_table.h", "imagebuf.h",
           "imagemeta.h", "limits.h", "lossless_jpeg_decoder.h", "matrix.h",
           "memory_data_accessor.h", "pipeline.h", "pipeline_element.h",
           "pipeline_processor.h", "pipeline_sink.h", "pipeline_source.h",
           "pipeline_accumulator.h", "pipeline_task.h",
           "pipeline_task_runner.h", "status_macros.h",
           "scale_data_processor.h", "sony_raw_file_parser.h",
           "sony_raw_pipeline_source.h", "tiff.h", "tiff_export_sink.h",
           "tiff_file_parser.h", "tiff_ifd.h", "tiff_tag.h", "tiff_tag_id.h",
           "tiff_pipeline_source.h", "tone_curve_processor.h",
           "white_balance_processor.h" ],
   deps = ["@abseil-cpp//absl/cleanup:cleanup",
           "@abseil-cpp//absl/container:flat_hash_map",
           "@abseil-cpp//absl/strings",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor",
           "@glog//:glog"],
   visibility = ["//visibility:public"],
)

cc_test(
   name = "imagebuf_test",
   size = "small",
   srcs = ["imagebuf.h", "imagebuf_test.cc"],
   deps = ["@googletest//:gtest_main"],
)

cc_test(
   name = "imagemeta_test",
   size = "small",
   srcs = ["colorspace.h", "imagemeta.h", "imagemeta.cc", "imagemeta_test.cc",
           "matrix.h"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/container:flat_hash_map",
           "@abseil-cpp//absl/status:statusor",
           "@glog//:glog"],
)

cc_test(
   name = "matrix_test",
   size = "small",
   srcs = ["matrix.h", "matrix_test.cc"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:statusor",
           "@abseil-cpp//absl/strings:str_format"],
)
cc_test(
   name = "tiff_tag_test",
   size = "small",
   srcs = ["endian.h", "status_macros.h",
           "tiff_tag.h", "tiff_tag_id.h", "tiff_tag.cc", "tiff_tag_test.cc",
           "data_accessor.h", "memory_data_accessor.h", "memory_data_accessor.cc"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor"],
)

cc_test(
   name = "tiff_ifd_test",
   size = "small",
   srcs = ["endian.h", "status_macros.h",
           "tiff_ifd.h", "tiff_ifd.cc", "tiff_ifd_test.cc",
           "tiff_tag.h", "tiff_tag_id.h", "tiff_tag.cc",
           "data_accessor.h", "memory_data_accessor.h", "memory_data_accessor.cc"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/container:flat_hash_map",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor"],
)

cc_test(
   name = "tiff_file_parser_test",
   size = "small",
   srcs = ["endian.h", "status_macros.h",
           "tiff_tag.h", "tiff_tag.cc", "tiff_ifd.h", "tiff_ifd.cc",
           "tiff.h", "tiff_tag_id.h",
           "file_parser.h", "tiff_file_parser.h", "tiff_file_parser.cc",
           "tiff_file_parser_test.cc",
           "data_accessor.h", "memory_data_accessor.h", "memory_data_accessor.cc"],
   deps = ["@googletest//:gtest_main",
           "@glog//:glog",
           "@abseil-cpp//absl/container:flat_hash_map",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor"],
)

cc_test(
   name = "data_accessor_test",
   size = "small",
   srcs = ["data_accessor.h",
           "file_data_accessor.h", "file_data_accessor.cc",
           "memory_data_accessor.h", "memory_data_accessor.cc",
           "data_accessor_test.cc"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:status"],
   data = ["test_data/file_accessor_data.bin"],
)

cc_test(
   name = "endian_test",
   size = "small",
   srcs = ["endian.h", "endian_test.cc"],
   deps = ["@googletest//:gtest_main"],
)

cc_test(
   name = "bit_pump_test",
   size = "small",
   srcs = ["bit_pump.h", "bit_pump.cc", "bit_pump_test.cc", "data_accessor.h",
           "endian.h", "memory_data_accessor.h", "memory_data_accessor.cc",
           "status_macros.h"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor",
           "@glog//:glog"],
)

cc_test(
   name = "jpeg_bit_pump_test",
   size = "small",
   srcs = ["bit_pump.h", "bit_pump.cc", "jpeg_bit_pump.h", "jpeg_bit_pump.cc",
           "jpeg_bit_pump_test.cc", "data_accessor.h", "endian.h",
           "memory_data_accessor.h", "memory_data_accessor.cc", "status_macros.h"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor",
           "@glog//:glog"],
)

cc_test(
   name = "huffman_table_test",
   size = "small",
   srcs = ["huffman_table.h", "huffman_table.cc", "huffman_table_test.cc",
           "data_accessor.h", "memory_data_accessor.h", "memory_data_accessor.cc",
           "limits.h", "status_macros.h"],
   deps = ["@googletest//:gtest_main",
           "@abseil-cpp//absl/status:status",
           "@abseil-cpp//absl/status:statusor",
           "@glog//:glog"],
)